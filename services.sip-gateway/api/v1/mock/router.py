import uuid
from pathlib import Path
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import <PERSON>R<PERSON>ponse, HTMLResponse

from context import <PERSON>Context
from dependencies import get_app_context_dependency

router = APIRouter(prefix="/mock")

"""
THIS IS A TEMPORARILY MOCK CLIENT THAT WILL POLL THE SIP GATEWAY TO KNOW WHEN THE CALL IS READY
IN FUTURE A REAL CLIENT WILL BE IMPLEMENTED
"""

@router.get("/ui/poll/{call_id}")
async def poll_call(
    call_id: str,
    context: ApplicationContext = Depends(get_app_context_dependency)
):
    session_id = context.session_manager.get_session(call_id)
    if session_id:
        return {"status": "connected", "media_session_id": session_id}
    return {"status": "ringing"}

@router.get("/ui", response_class=HTMLResponse)
async def test_ui():
    return FileResponse(Path(__file__).parent / "static" / "client.html")

@router.get("/ui/mediasoup-client.min.js")
async def mediasoup_client():
    return FileResponse(Path(__file__).parent / "static" / "mediasoup-client.min.js")

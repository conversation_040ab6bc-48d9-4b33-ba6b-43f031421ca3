from fastapi import APIRouter, Depends, Response

from context import ApplicationContext
from dependencies import get_app_context_dependency

from .schemas import CallConnectRequest

router = APIRouter(prefix="/internal")


@router.post("/connect")
async def connect_to_call(
    request: CallConnectRequest,
    context: ApplicationContext = Depends(get_app_context_dependency)
):
    """
    This endpoint is commanded by the Call Orchestrator to connect a SIP call to a media session.
    """
    print(f"Connecting call {request.call_id} to media session {request.media_session_id}")
    context.session_manager.add_session(request.call_id, request.media_session_id)

    # In a real example this might include a PlainTransport that will be used to send audio from the SIP call to the media session.
    # For now we just return a 200 OK, and let the Mock UI poll the /poll/{call_id} endpoint to check if the call has been connected.

    return Response(status_code=200)

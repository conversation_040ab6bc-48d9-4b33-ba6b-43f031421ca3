from fastapi import APIRouter, Depends

from context import ApplicationContext
from dependencies import get_app_context_dependency
from core.events import publish_sip_call_received_event
from .schemas import WebhookPayload

router = APIRouter(prefix="/webhook")


@router.post("/")
async def trigger_call(
    payload: WebhookPayload,
    context: ApplicationContext = Depends(get_app_context_dependency)
):
    """
    This endpoint is called by the SIP provider's webhook to notify us of a new call.
    """
    await publish_sip_call_received_event(context.event_producer, payload.call_id)
    return {"status": "published"}

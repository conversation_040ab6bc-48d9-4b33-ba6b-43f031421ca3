[tool.poetry]
name = "sip-gateway"
version = "0.1.0"
description = "Cortexa SIP Gateway Service"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
cortexa-common = {path = "../shared/cortexa-common", develop = true}
fastapi = "^0.116.1"
pydantic = "^2.10.1"
pydantic-settings = "^2.10.1"
uvicorn = {extras = ["standard"], version = "^0.35.0"}

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

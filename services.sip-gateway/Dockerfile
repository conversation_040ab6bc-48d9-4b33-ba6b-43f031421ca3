FROM python:3.12-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libsasl2-dev \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy Poetry files
COPY services.sip-gateway/pyproject.toml services.sip-gateway/poetry.lock* ./

# Copy cortexa-common package
COPY shared/cortexa-common /shared/cortexa-common

# Configure Poetry and install dependencies
RUN poetry config virtualenvs.create false && \
    poetry install --only=main --no-root && \
    rm -rf ~/.cache/pypoetry

# 2. Final Stage
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/shared/cortexa-common:/home/<USER>"

# Install runtime system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libsasl2-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user (robust to existing IDs)
RUN getent group appgroup || groupadd -r appgroup && \
    id -u appuser >/dev/null 2>&1 || useradd -r -g appgroup -d /home/<USER>

# Set work directory
WORKDIR /home/<USER>

# Copy Python packages from builder stage
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Ensure editable path dependency is available in final image
COPY shared/cortexa-common /shared/cortexa-common

# Copy application code
COPY --chown=appuser:appgroup services.sip-gateway/ .

# Switch to non-root user
USER appuser

EXPOSE 8010

HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8010/ui/test', timeout=10)" || exit 1

CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8010"]

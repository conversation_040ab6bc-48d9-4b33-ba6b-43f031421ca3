from fastapi import FastAPI
from contextlib import asynccontextmanager

from config import settings
from context import ApplicationContext

from api.v1.internal.router import router as internal_router
from api.v1.mock.router import router as mock_router
from api.v1.webhook.router import router as webhook_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    context = await ApplicationContext.get_instance()
    await context.initialize()
    yield
    await context.cleanup()


app = FastAPI(
    title="SIP Gateway Service",
    lifespan=lifespan,
    host=settings.host,
    port=settings.port,
)

app.include_router(internal_router)
app.include_router(mock_router)
app.include_router(webhook_router)

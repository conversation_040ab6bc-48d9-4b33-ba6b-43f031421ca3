[tool.poetry]
name = "cortexa-common"
version = "0.1.0"
description = "Common utilities and shared code for Cortexa microservices"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "cortexacommon"}]

[tool.poetry.dependencies]
python = "^3.11"
pydantic = {extras = ["email"], version = "^2.11.7"}
pydantic-settings = "^2.10.1"
aiokafka = "^0.12.0"
loguru = "^0.7.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
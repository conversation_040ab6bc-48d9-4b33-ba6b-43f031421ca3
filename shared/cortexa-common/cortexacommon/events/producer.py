import json
import logging
from typing import Any
from uuid import UUID

from aiokafka import AIOKafkaProducer

from ..config import KafkaSettings
from .schemas import BaseEvent

logger = logging.getLogger(__name__)


class EventProducer:
    """Kafka event producer for publishing events."""

    def __init__(self, kafka_settings: KafkaSettings):
        """Initialize the event producer."""
        self.kafka_settings = kafka_settings
        self.producer: AIOKafkaProducer | None = None
    
    async def start(self) -> None:
        """Start the Kafka producer."""
        self.producer = AIOKafkaProducer(
            bootstrap_servers=self.kafka_settings.bootstrap_servers,
            value_serializer=lambda v: json.dumps(v, default=self._json_serializer).encode('utf-8'),
            key_serializer=lambda k: k.encode('utf-8') if k else None,
        )
        await self.producer.start()
        logger.info("Kafka producer started")
    
    async def stop(self) -> None:
        """Stop the Kafka producer."""
        if self.producer:
            await self.producer.stop()
            logger.info("Kafka producer stopped")
    
    async def publish_event(
        self,
        topic: str,
        event: BaseEvent,
        key: str | None = None
    ) -> None:
        """Publish an event to a Kafka topic."""
        if not self.producer:
            raise RuntimeError("Producer not started. Call start() first.")
        
        try:
            event_data = event.model_dump()
            await self.producer.send(topic, value=event_data, key=key)
            logger.info(f"Published event {event.event_id} to topic {topic}")
        except Exception as e:
            logger.error(f"Failed to publish event {event.event_id} to topic {topic}: {e}")
            raise
    
    async def publish_dict(
        self,
        topic: str,
        data: dict[str, Any],
        key: str | None = None
    ) -> None:
        """Publish a dictionary as an event to a Kafka topic."""
        if not self.producer:
            raise RuntimeError("Producer not started. Call start() first.")
        
        try:
            await self.producer.send(topic, value=data, key=key)
            logger.info(f"Published data to topic {topic}")
        except Exception as e:
            logger.error(f"Failed to publish data to topic {topic}: {e}")
            raise
    
    @staticmethod
    def _json_serializer(obj: Any) -> Any:
        """JSON serializer for complex objects."""
        if isinstance(obj, UUID):
            return str(obj)
        elif hasattr(obj, 'isoformat'):  # datetime objects
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()

from typing import Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="DB_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    uri: str = Field(default="postgresql+psycopg://user:password@localhost:5432/cortexa", description="Database URI")


class KafkaSettings(BaseSettings):
    """Kafka configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="KAFKA_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    bootstrap_servers: str = Field(default="localhost:9092", description="Kafka bootstrap servers")
    group_id: str = Field(default="cortexa-group", description="Kafka consumer group ID")


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="LOG_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    level: str = Field(
        default="INFO",
        description="Log level"
    )
    file: str | None = Field(
        default=None,
        description="Log file path"
    )
    max_file_size: str = Field(
        default="100 MB",
        description="Maximum log file size"
    )
    retention: str = Field(
        default="30 days",
        description="Log file retention period"
    )


class MonitoringSettings(BaseSettings):
    """Monitoring configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="MONITORING_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    metrics_enabled: bool = Field(
        default=True,
        description="Enable metrics collection"
    )
    tracing_enabled: bool = Field(
        default=True,
        description="Enable distributed tracing"
    )

import asyncio

from config import settings
from core.participant import ParticipantRepository

from cortexacommon.events.producer import EventProducer


class ApplicationContext:
    """
    Application context for managing shared resources.
    This class provides a singleton pattern for managing application-wide
    resources like Kafka producers/consumers and external service clients.
    """

    _instance: 'ApplicationContext | None' = None
    _lock = asyncio.Lock()

    def __init__(self):
        """Initialize the application context."""
        self._event_producer: EventProducer | None = None
        self._participant_repository: ParticipantRepository | None = None
        self._initialized = False

    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        """
        Get the singleton instance of the application context.
        
        Returns:
            ApplicationContext: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @property
    def event_producer(self) -> EventProducer:
        """Get the event producer instance."""
        assert self._event_producer is not None, "Event producer not initialized"
        return self._event_producer
    
    @property
    def participant_repository(self) -> ParticipantRepository:
        """Get the session manager instance."""
        assert self._participant_repository is not None, "Participant repository not initialized"
        return self._participant_repository
    
    @property
    def is_initialized(self) -> bool:
        """Check if the application context is initialized."""
        return self._initialized
    
    async def initialize(self) -> None:
        """
        Initialize the application context and its resources.
        
        This method initializes all shared resources including Kafka
        producers/consumers and external service clients.
        """
        if self._initialized:
            return
        
        try:
            # Initialize events manager
            self._event_producer = EventProducer(settings.kafka_settings)
            await self._event_producer.start()

            # Initialize participant repository
            self._participant_repository = ParticipantRepository()

            self._initialized = True
        except Exception as e:
            raise RuntimeError(f"Failed to initialize application context: {e}")
    
    async def cleanup(self) -> None:
        """
        Clean up the application context and its resources.
        
        This method should be called during application shutdown.
        """
        try:
            if self._event_producer:
                await self._event_producer.stop()
                self._event_producer = None

            self._initialized = False
        except Exception as e:
            print(f"Error during application context cleanup: {e}")

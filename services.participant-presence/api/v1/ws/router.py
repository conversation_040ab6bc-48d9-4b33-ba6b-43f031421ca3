import json
from jose import jwt, JW<PERSON>rror
from fastapi import APIRouter, Depends, WebSocket, Query
from cortexacommon.logging import get_logger

from config import settings
from context import ApplicationContext
from dependencies import get_app_context_dependency
from core.participant import Participant, ParticipantStatus

from .schema import UpdateStatusMessage

ALGORITHMS = ["HS256"]

router = APIRouter(prefix="/bob")
logger = get_logger(__name__)


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(None),
    context: ApplicationContext = Depends(get_app_context_dependency)
):
    # Security
    # TODO: abstract this into the cortexacommon
    try:
        payload = jwt.decode(token, settings.supabase_jwt_secret, algorithms=ALGORITHMS,
                             audience="authenticated", options={"verify_signature": False})
        participant_id = payload["sub"]
        participant_role = payload["app_role"]
        logger.info(f"User {participant_id} connecting as {participant_role}")
    except JWTError as e:
        logger.warning(f"Failed to decode token: {e}")
        await websocket.close(code=4001, reason=f"Invalid token: {str(e)}")
        return
    except Exception as e:
        logger.warning(f"Authentication failed: {e}")
        await websocket.close(code=4000, reason=f"Authentication failed: {str(e)}")
        return

    # Accept connection only after successful validation
    await websocket.accept()

    participant = context.participant_repository.get_participant(participant_id)
    if not participant:
        participant = Participant(participant_id, participant_role, ParticipantStatus.AVAILABLE)
        context.participant_repository.add_participant(participant)
        logger.info(f"New participant added: {participant}")

    else:
        logger.info(f"Existing participant connected: {participant}")
        context.participant_repository.update_participant_status(participant_id, ParticipantStatus.AVAILABLE)

    try:
        # Keep the connection alive and handle incoming messages
        while True:
            data = await websocket.receive_text()
            message = UpdateStatusMessage.model_validate_json(data)
            context.participant_repository.update_participant_status(participant_id, message.status)
            logger.info(f"Participant {participant_id} updated status to {message.status}")

    except Exception:
        pass # Connection dropped or error occurred

    finally:
        # Set participant as OFFLINE when they disconnect
        logger.info(f"Participant disconnected: {participant}")
        context.participant_repository.update_participant_status(participant_id, ParticipantStatus.OFFLINE)#
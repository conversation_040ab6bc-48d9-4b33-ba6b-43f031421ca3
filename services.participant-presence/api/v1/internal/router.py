from fastapi import APIRouter, Depends, Query

from context import ApplicationContext
from dependencies import get_app_context_dependency
from core.participant import Participant


router = APIRouter(prefix="/internal")

@router.get("/participants")
def get_available_participants(
    role: str = Query(None),
    status: str = Query(None),
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> list[Participant]:
    return context.participant_repository.get_participants(role, status)

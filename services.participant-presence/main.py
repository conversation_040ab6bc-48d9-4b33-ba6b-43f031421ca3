from contextlib import asynccontextmanager
from fastapi import FastAPI

from config import settings
from context import ApplicationContext
from api.v1.ws.router import router as ws_router
from api.v1.internal.router import router as internal_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    context = await ApplicationContext.get_instance()
    await context.initialize()
    yield
    await context.cleanup()

app = FastAPI(
    host=settings.host,
    port=settings.port,
    lifespan=lifespan,
)

app.include_router(ws_router)
app.include_router(internal_router)

from enum import Enum
from dataclasses import dataclass


class ParticipantStatus(str, Enum):
    AVAILABLE = "available"
    BUSY = "busy"
    OFFLINE = "offline"


@dataclass
class Participant:
    id: str
    role: str
    status: ParticipantStatus


class ParticipantRepository:
    """
    Repository for managing participants.

    In the future this will interface with Redis
    """
    def __init__(self):
        self.participants: list[Participant] = []

    def add_participant(self, participant: Participant) -> None:
        self.participants.append(participant)
    
    def remove_participant(self, participant_id: str) -> None:
        self.participants = [p for p in self.participants if p.id != participant_id]

    def get_participants(self, role: str | None = None, status: str | None = None) -> list[Participant]:
        users = []
        for participant in self.participants:
            if role and participant.role != role:
                continue
            if status and participant.status != status:
                continue

            users.append(participant)

        return users

    def get_participant(self, participant_id: str) -> Participant | None:
        for participant in self.participants:
            if participant.id == participant_id:
                return participant
        return None


    def update_participant_status(self, participant_id: str, status: ParticipantStatus) -> None:
        for participant in self.participants:
            if participant.id == participant_id:
                participant.status = status
                return

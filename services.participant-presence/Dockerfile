# Multi-stage Dockerfile for Participant Presence Service

# 1. Builder Stage
FROM python:3.12-alpine AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apk add --no-cache \
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev \
    zlib-dev \
    curl \
    git

# Install Poetry
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy Poetry files
COPY services.participant-presence/pyproject.toml services.participant-presence/poetry.lock* ./

# Copy cortexa-common package
COPY shared/cortexa-common /shared/cortexa-common

# Configure Poetry and install dependencies
RUN poetry config virtualenvs.create false && \
    poetry install --only=main --no-root && \
    rm -rf ~/.cache/pypoetry

# 2. Final Stage
FROM python:3.12-alpine

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/shared/cortexa-common:/home/<USER>"

# Install runtime system dependencies
RUN apk add --no-cache \
    libffi \
    openssl \
    curl

# Create a non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set work directory
WORKDIR /home/<USER>

# Copy Python packages from builder stage
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Ensure editable path dependency is available in final image
COPY shared/cortexa-common /shared/cortexa-common

# Copy application code (only this service)
COPY --chown=appuser:appgroup services.participant-presence/ .

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8001/docs', timeout=10)" || exit 1

# Command to run the application
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]

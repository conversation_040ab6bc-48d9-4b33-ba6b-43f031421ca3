[tool.poetry]
name = "user-management"
version = "0.1.0"
description = "Cortexa User Management Service"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
cortexa-common = {path = "../shared/cortexa-common", develop = true}
fastapi = "^0.116.1"
pydantic-settings = "^2.10.1"
supabase = "^2.18.1"
uvicorn = "^0.35.0"
pydantic = {version = "^2.11.7", extras = ["email"]}
loguru = "^0.7.3"
python-jose = {extras = ["cryptography"], version = "^3.5.0"}

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

from fastapi import APIRouter, Response, Depends

from dependencies import get_app_context_dependency
from context import ApplicationContext

router = APIRouter(prefix="/calls")


@router.post("/{call_id}/accept")
async def accept_call(call_id: str, context: ApplicationContext = Depends(get_app_context_dependency)) -> Response:
    await context.call_handler.accept_call(call_id)
    return Response(status_code=202)

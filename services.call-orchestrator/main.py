from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager

from config import settings
from context import ApplicationContext
from consumers.call_sip_received import setup_sip_call_received_consumer

from api.v1.call.router import router as call_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize application context
    context = await ApplicationContext.get_instance()
    await context.initialize()

    # Initialize consumers and store on context for proper shutdown
    events_consumer, consume_task = await setup_sip_call_received_consumer(
        context.call_handler.on_sip_call_received
    )

    yield

    await events_consumer.stop()
    consume_task.cancel()

    await context.cleanup()

app = FastAPI(
    title="Call Orchestrator Service",
    lifespan=lifespan,
    host=settings.host,
    port=settings.port,
)

app.include_router(call_router)
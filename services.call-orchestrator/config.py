from pydantic_settings import BaseSettings, SettingsConfigDict
from cortexacommon.config import KafkaSettings, DatabaseSettings


class Settings(BaseSettings):
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    service_name: str = "call-orchestrator"
    kafka_settings: KafkaSettings = KafkaSettings()
    database: DatabaseSettings = DatabaseSettings()

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()

from __future__ import annotations

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "20250904_000001"
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.create_table(
        "calls",
        sa.Column("id", sa.String(), primary_key=True, nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("status", sa.String(), nullable=False, server_default="received"),
        sa.Column("media_session_id", sa.String(), nullable=True),
    )
    op.create_index("ix_calls_id", "calls", ["id"])  # helpful index


def downgrade() -> None:
    op.drop_index("ix_calls_id", table_name="calls")
    op.drop_table("calls")


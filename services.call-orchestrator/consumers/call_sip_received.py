import asyncio
from typing import Callable, <PERSON>waitable, <PERSON><PERSON>
from config import settings

from cortexacommon.logging import get_logger
from cortexacommon.events.consumer import EventConsumer
from cortexacommon.events.schemas import SipCallReceivedEvent

logger = get_logger(__name__)

async def setup_sip_call_received_consumer(handler: Callable[[str], Awaitable[None]]) -> Tuple[EventConsumer, asyncio.Task]:
    """
    Create, start, and begin consuming from the SIP call received topic.

    Returns the EventConsumer instance and the background asyncio.Task running the consume loop,
    so callers can manage lifecycle (store on context and stop during shutdown).
    """
    events_consumer = EventConsumer(
        kafka_settings=settings.kafka_settings,
        topics=["sip-gateway.sip-call-received"],
    )

    async def _handle_sip_call_received(payload: dict) -> None:
        event = SipCallReceivedEvent(**payload)
        logger.info(f"Received sip call received event: {event}")
        # Pass only the data the handler expects (call_id)
        await handler(event.call_id)

    events_consumer.register_handler("sip-gateway.sip-call-received", _handle_sip_call_received)

    logger.info("Starting SIP call received consumer")
    await events_consumer.start()

    # Run the consume loop in the background
    consume_task = asyncio.create_task(events_consumer.consume(), name="sip-call-received-consumer")

    return events_consumer, consume_task

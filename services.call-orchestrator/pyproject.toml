[tool.poetry]
name = "call-orchestrator"
version = "0.1.0"
description = "Cortexa Call Orchestrator Service"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
cortexa-common = {path = "../shared/cortexa-common", develop = true}
fastapi = "^0.116.1"
pydantic = "^2.10.1"
pydantic-settings = "^2.10.1"
uvicorn = {extras = ["standard"], version = "^0.35.0"}
sqlalchemy = {version = "^2.0.43", extras = ["asyncio"]}
psycopg = {version = "^3.2.1", extras = ["binary"]}
alembic = "^1.13.2"
httpx = "^0.27.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

import uuid
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from .models import Call


class CallRepository:
    def __init__(self, session: AsyncSession):
        self._session = session

    async def get(self, call_id: str | uuid.UUID) -> Call | None:
        result = await self._session.execute(select(Call).where(Call.id == call_id))
        return result.scalar_one_or_none()

    async def create_if_not_exists(self, call_id: str | uuid.UUID) -> Call:
        existing = await self.get(call_id)
        if existing:
            return existing
        call = Call(id=call_id)
        self._session.add(call)
        await self._session.flush()
        return call

    async def set_media_session(self, call_id: str | uuid.UUID, media_session_id: str) -> None:
        await self._session.execute(
            update(Call).where(Call.id == call_id).values(media_session_id=media_session_id)
        )

    async def set_status(self, call_id: str | uuid.UUID, status: str) -> None:
        await self._session.execute(
            update(Call).where(Call.id == call_id).values(status=status)
        )


from datetime import datetime, timezone

from sqlalchemy import String, DateTime, MetaData
from sqlalchemy.orm import Mapped, DeclarativeBase, mapped_column


metadata_obj = MetaData()


class Base(DeclarativeBase):
    metadata = metadata_obj


class Call(Base):
    __tablename__ = "calls"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    status: Mapped[str] = mapped_column(String, default="received", nullable=False)
    media_session_id: Mapped[str | None] = mapped_column(String, nullable=True)

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from cortexacommon.config import DatabaseSettings


class Database:
    def __init__(self, options: DatabaseSettings):
        self._options = options
        self._db_engine = None
        self._session_factory = None

    async def initialize(self) -> None:
        self._db_engine = create_async_engine(
                self._options.uri,
                pool_pre_ping=True,
                pool_size=5,
                max_overflow=5,
            )
        
        self._session_factory = async_sessionmaker(self._db_engine, expire_on_commit=False)
        await self._test_connection()
        
    async def _test_connection(self) -> None:
        assert self._db_engine is not None, "DB engine not initialized"
        async with self._db_engine.connect() as conn:
            await conn.execute(text("SELECT 1"))

    async def cleanup(self) -> None:
        pass

    @property
    def session_factory(self) -> async_sessionmaker[AsyncSession]:
        assert self._session_factory is not None, "DB session factory not initialized"
        return self._session_factory

from core.db import Database
from core.repositories import CallRepository

from config import settings
from cortexacommon.events.producer import EventProducer
from cortexacommon.events.schemas import CallStartedEvent

from .sip_gateway import SIPGatewayService
from .voice_router import VoiceRouterService


class CallHandler:
    """
    High-level wrapper that orchestrates call lifecycle using repositories and services.
    """

    def __init__(
        self,
        db: Database,
        sip_gateway: SIPGatewayService,
        voice_router: VoiceRouterService,
    ):
        self._db = db
        self._sip_gateway = sip_gateway
        self._voice_router = voice_router
        self._producer: EventProducer | None = None

    async def initialize(self) -> None:
        # Initialize a producer lazily to publish call events
        self._producer = EventProducer(settings.kafka_settings)
        await self._producer.start()

    async def cleanup(self) -> None:
        if self._producer:
            await self._producer.stop()
            self._producer = None

    async def on_sip_call_received(self, call_id: str) -> None:
        """
        Handle incoming SIP call events.
        """
        async with self._db.session_factory() as session:
            async with session.begin():
                repo = CallRepository(session)
                await repo.create_if_not_exists(call_id)
                # TODO: create Transports with Voice Router

        # publish event after commit
        if self._producer:
            event = CallStartedEvent(call_id=call_id, source_service="call-orchestrator")
            await self._producer.publish_event("call-orchestrator.call-started", event)

    async def accept_call(self, call_id: str) -> None:
        """
        Accept a call:
        - Create media session in Voice Router
        - Command SIP Gateway to connect call to media session
        - Persist media_session_id and status
        """
        async with self._db.session_factory() as session:
            async with session.begin():
                repo = CallRepository(session)
                call = await repo.create_if_not_exists(call_id)

                media_session_id = await self._voice_router.create_media_session()
                await repo.set_media_session(call_id, media_session_id)

        # connect after commit to avoid side effects on rollback
        await self._sip_gateway.connect_call(call_id=call_id, media_session_id=media_session_id)



import { Router } from 'express';
import { SessionManager } from '../../services/SessionManager';
import * as controller from './controller';

export function createApiRouter(sessionManager: SessionManager): Router {
  const router = Router();

  // Middleware to inject the SessionManager into each request
  router.use((req, res, next) => {
    req.sessionManager = sessionManager;
    next();
  });

  /**
   * Creates a new media session.
   * POST /internal/sessions
   */
  router.post('/internal/sessions', controller.createSession);

  /**
   * Terminates an entire media session.
   * DELETE /internal/sessions/{sessionId}
   */
  router.delete('/internal/sessions/:sessionId', controller.deleteSession);

  // /**
  //  * Starts an audio fork for a given session.
  //  * POST /internal/sessions/{sessionId}/forks
  //  */
  // router.post('/internal/sessions/:sessionId/forks', controller.startFork);

  // /**
  //  * Stops a specific audio fork.
  //  * DELETE /internal/sessions/{sessionId}/forks/{forkId}
  //  */
  // router.delete('/internal/sessions/:sessionId/forks/:forkId', controller.stopFork);

  return router;
}
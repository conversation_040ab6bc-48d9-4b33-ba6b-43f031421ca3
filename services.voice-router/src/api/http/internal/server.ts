import express from 'express';
import http from 'http';
import { SessionManager } from '../../services/SessionManager';
import { createApiRouter } from './routes';

/**
 * Creates and configures the Express HTTP server.
 * @param sessionManager - The singleton instance of the SessionManager.
 * @returns An http.Server instance.
 */
export function createHttpServer(sessionManager: SessionManager): http.Server {
  const app = express();
  const httpServer = http.createServer(app);

  // Apply middleware
  app.use(express.json()); // For parsing application/json

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).send('OK');
  });

  // Create and apply the API router
  const apiRouter = createApiRouter(sessionManager);
  app.use(apiRouter);

  return httpServer;
}

services:
  user-management:
    build:
      context: .
      dockerfile: services.user-management/Dockerfile
    env_file:
      - .env.supabase.docker

  call-orchestrator:
    build:
      context: .
      dockerfile: services.call-orchestrator/Dockerfile
    env_file:
      - .env.supabase.docker
    environment:
      - DB_URI=postgresql+psycopg://user:password@postgres:5432/cortexa
    depends_on:
      call-orchestrator-init:
        condition: service_completed_successfully

  voice-router:
    build:
      context: ./services.voice-router
      dockerfile: Dockerfile

  # queuing-service:
  #   build:
  #     context: .
  #     dockerfile: services.queuing-service/Dockerfile
  #   environment:
  #     - PARTICIPANT_PRESENCE_URL=http://participant-presence:8001

  participant-presence:
    build:
      context: .
      dockerfile: services.participant-presence/Dockerfile
    env_file:
      - .env.supabase.docker
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092

  sip-gateway:
    build:
      context: .
      dockerfile: services.sip-gateway/Dockerfile
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - VOICE_ROUTER_WS=ws://voice-router:3001
    networks:
      - internal
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mock-caller.rule=Host(`localhost`) && PathPrefix(`/caller`)"
      - "traefik.http.routers.mock-caller.entrypoints=web"
      - "traefik.http.services.mock-caller.loadbalancer.server.port=8010"
    ports:
      - "8010:8010"

  postgres:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d cortexa"]
      interval: 5s
      timeout: 5s
      retries: 20
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    env_file:
      - .env.supabase.docker

  call-orchestrator-init:
    build:
      context: .
      dockerfile: services.call-orchestrator/Dockerfile
    env_file:
      - .env.supabase.docker
    environment:
      - DB_URI=postgresql+psycopg://user:password@postgres:5432/cortexa
    depends_on:
      postgres:
        condition: service_healthy
    command: >
      sh -lc '
      echo "Running database migrations...";
      CURRENT_REVISION=$(alembic current 2>&1 | grep -v "INFO" | grep -E "^20[0-9]{6}_[0-9]{6}" | head -1);
      if [ -z "$CURRENT_REVISION" ]; then
        echo "No migration history found. Stamping current state...";
        alembic stamp head >/dev/null 2>&1 || echo "Stamp failed, continuing...";
      else
        echo "Migration history found: $CURRENT_REVISION";
      fi;
      alembic upgrade head || echo "Upgrade completed with warnings.";
      echo "Migration setup complete.";
      '
    restart: "no"
    networks:
      - internal